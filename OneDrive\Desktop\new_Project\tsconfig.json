{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "rootDir": "./src", "outDir": "./dist", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["*"], "@config/*": ["config/*"], "@controllers/*": ["controllers/*"], "@middleware/*": ["middleware/*"], "@models/*": ["models/*"], "@routes/*": ["routes/*"], "@services/*": ["services/*"], "@repositories/*": ["repositories/*"], "@interfaces/*": ["interfaces/*"], "@validators/*": ["validators/*"], "@utils/*": ["utils/*"], "@types/*": ["types/*"], "@tests/*": ["tests/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}