{"name": "new_project", "version": "1.0.0", "description": "A Node.js TypeScript API with MongoDB integration", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "start:dev": "npm run build && npm start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["nodejs", "typescript", "express", "mongodb", "api", "rest"], "author": "", "license": "ISC", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"dotenv": "^17.2.0", "express": "^5.1.0", "mongoose": "^8.16.3"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.0.14", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}