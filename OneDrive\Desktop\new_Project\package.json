{"name": "new_project", "version": "1.0.0", "description": "A Node.js TypeScript API boilerplate with MongoDB, authentication, and comprehensive tooling", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only --require tsconfig-paths/register src/server.ts", "build": "tsc", "start": "node dist/server.js", "start:dev": "npm run build && npm start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.{ts,js,json}", "format:check": "prettier --check src/**/*.{ts,js,json}", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "husky", "pre-commit": "lint-staged"}, "keywords": ["nodejs", "typescript", "express", "mongodb", "api", "boilerplate", "jwt", "authentication"], "author": "", "license": "ISC", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.3", "morgan": "^1.10.0"}, "devDependencies": {"@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.14", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "husky": "^9.1.7", "jest": "^30.0.4", "lint-staged": "^16.1.2", "nodemon": "^3.1.10", "prettier": "^3.6.2", "rimraf": "^6.0.1", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "lint-staged": {"src/**/*.{ts,js}": ["eslint --fix", "prettier --write"], "src/**/*.{json,md}": ["prettier --write"]}}