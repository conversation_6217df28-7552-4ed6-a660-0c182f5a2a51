import nodemailer from 'nodemailer';
import { logger } from '@utils/logger';

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      const mailOptions = {
        from: `"${process.env.APP_NAME || 'Your App'}" <${process.env.EMAIL_USER}>`,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text,
      };

      const info = await this.transporter.sendMail(mailOptions);
      logger.info(`Email sent successfully to ${options.to}`, { messageId: info.messageId });
      return true;
    } catch (error) {
      logger.error('Failed to send email', { error, to: options.to });
      return false;
    }
  }

  async sendOTPEmail(email: string, otp: string, type: string): Promise<boolean> {
    const subject = this.getOTPEmailSubject(type);
    const html = this.getOTPEmailTemplate(otp, type);
    const text = this.getOTPEmailText(otp, type);

    return await this.sendEmail({
      to: email,
      subject,
      html,
      text,
    });
  }

  private getOTPEmailSubject(type: string): string {
    switch (type) {
      case 'email_verification':
        return 'Verify Your Email Address';
      case 'login_verification':
        return 'Login Verification Code';
      case 'password_reset':
        return 'Reset Your Password';
      default:
        return 'Verification Code';
    }
  }

  private getOTPEmailTemplate(otp: string, type: string): string {
    const appName = process.env.APP_NAME || 'Your App';
    
    let title = '';
    let message = '';
    
    switch (type) {
      case 'email_verification':
        title = 'Verify Your Email Address';
        message = 'Please use the following code to verify your email address:';
        break;
      case 'login_verification':
        title = 'Login Verification';
        message = 'Please use the following code to complete your login:';
        break;
      case 'password_reset':
        title = 'Reset Your Password';
        message = 'Please use the following code to reset your password:';
        break;
      default:
        title = 'Verification Code';
        message = 'Please use the following verification code:';
    }

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .otp-code { 
            font-size: 32px; 
            font-weight: bold; 
            text-align: center; 
            background: #f4f4f4; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 8px;
            letter-spacing: 5px;
          }
          .footer { margin-top: 30px; font-size: 14px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${appName}</h1>
            <h2>${title}</h2>
          </div>
          
          <p>Hello,</p>
          <p>${message}</p>
          
          <div class="otp-code">${otp}</div>
          
          <p><strong>This code will expire in 10 minutes.</strong></p>
          <p>If you didn't request this code, please ignore this email.</p>
          
          <div class="footer">
            <p>Best regards,<br>The ${appName} Team</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private getOTPEmailText(otp: string, type: string): string {
    const appName = process.env.APP_NAME || 'Your App';
    
    let message = '';
    switch (type) {
      case 'email_verification':
        message = 'Please use the following code to verify your email address';
        break;
      case 'login_verification':
        message = 'Please use the following code to complete your login';
        break;
      case 'password_reset':
        message = 'Please use the following code to reset your password';
        break;
      default:
        message = 'Please use the following verification code';
    }

    return `
${appName}

${message}: ${otp}

This code will expire in 10 minutes.

If you didn't request this code, please ignore this email.

Best regards,
The ${appName} Team
    `.trim();
  }

  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info('Email service connection verified successfully');
      return true;
    } catch (error) {
      logger.error('Email service connection failed', { error });
      return false;
    }
  }
}

export const emailService = new EmailService();
