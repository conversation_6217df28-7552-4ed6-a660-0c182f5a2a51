import { generateOTP } from '@models/OTP.model';
import { emailService } from '@services/email.service';
import { otpRepository } from '@repositories/otp.repository';
import { logger } from '@utils/logger';
import { ValidationError } from '@utils/AppError';
import { IOTPService } from '@/interfaces/service.interface';

class OTPService implements IOTPService {
  async generateAndSendOTP(
    email: string,
    type: 'email_verification' | 'password_reset' | 'login_verification'
  ): Promise<boolean> {
    try {
      // Delete any existing OTPs for this email and type
      await otpRepository.deleteOTPsByEmail(email, type);

      // Generate new OTP
      const otpCode = generateOTP();

      // Save OTP to database
      const otp = await otpRepository.create({
        email,
        otp: otpCode,
        type,
        expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
      });

      // Send OTP via email
      const emailSent = await emailService.sendOTPEmail(email, otpCode, type);

      if (!emailSent) {
        // If email failed, delete the OTP
        await otpRepository.deleteById(String(otp._id));
        throw new Error('Failed to send OTP email');
      }

      logger.info(`OTP generated and sent for ${email}`, { type });
      return true;
    } catch (error) {
      logger.error('Failed to generate and send OTP', { error, email, type });
      return false;
    }
  }

  async verifyOTP(
    email: string,
    otpCode: string,
    type: 'email_verification' | 'password_reset' | 'login_verification'
  ): Promise<boolean> {
    try {
      // Find the OTP
      const otp = await otpRepository.findOTPByEmailAndCode(email, otpCode, type);

      if (!otp) {
        throw new ValidationError('Invalid or expired OTP');
      }

      // Check if OTP is expired
      if (otp.isExpired()) {
        await otpRepository.deleteById(String(otp._id));
        throw new ValidationError('OTP has expired');
      }

      // Mark OTP as used
      await otpRepository.markAsUsed(String(otp._id));

      logger.info(`OTP verified successfully for ${email}`, { type });
      return true;
    } catch (error) {
      logger.error('OTP verification failed', { error, email, type });
      throw error;
    }
  }

  async cleanupExpiredOTPs(): Promise<void> {
    try {
      const result = await otpRepository.deleteExpiredOTPs();
      logger.info(`Cleaned up ${result.deletedCount} expired/used OTPs`);
    } catch (error) {
      logger.error('Failed to cleanup expired OTPs', { error });
    }
  }

  async resendOTP(
    email: string,
    type: 'email_verification' | 'password_reset' | 'login_verification'
  ): Promise<boolean> {
    try {
      // Check if there's a recent OTP (within last 1 minute to prevent spam)
      const hasRecent = await otpRepository.hasRecentOTP(email, type, 1);

      if (hasRecent) {
        throw new ValidationError('Please wait before requesting a new OTP');
      }

      return await this.generateAndSendOTP(email, type);
    } catch (error) {
      logger.error('Failed to resend OTP', { error, email, type });
      throw error;
    }
  }

  async hasValidOTP(
    email: string,
    type: 'email_verification' | 'password_reset' | 'login_verification'
  ): Promise<boolean> {
    try {
      const otp = await otpRepository.findValidOTP(email, type);
      return !!otp;
    } catch (error) {
      logger.error('Failed to check for valid OTP', { error, email, type });
      return false;
    }
  }
}

export const otpService = new OTPService();
