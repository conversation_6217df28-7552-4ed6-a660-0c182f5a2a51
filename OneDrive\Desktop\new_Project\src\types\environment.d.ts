declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: 'development' | 'production' | 'test';
      PORT: string;
      MONGO_URI: string;
      JWT_SECRET: string;
      JWT_EXPIRE: string;
      CORS_ORIGIN: string;
      RATE_LIMIT_WINDOW_MS: string;
      RATE_LIMIT_MAX_REQUESTS: string;
      LOG_LEVEL: 'error' | 'warn' | 'info' | 'debug';
      EMAIL_HOST?: string;
      EMAIL_PORT?: string;
      EMAIL_USER?: string;
      EMAIL_PASS?: string;
      MAX_FILE_SIZE?: string;
      UPLOAD_PATH?: string;
      API_VERSION?: string;
    }
  }
}

export {};
