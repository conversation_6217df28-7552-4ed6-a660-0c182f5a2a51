"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const dotenv_1 = __importDefault(require("dotenv"));
const security_1 = require("@middleware/security");
const errorHandler_1 = require("@middleware/errorHandler");
const auth_routes_1 = __importDefault(require("@routes/auth.routes"));
const user_routes_1 = __importDefault(require("@routes/user.routes"));
const logger_1 = require("@utils/logger");
dotenv_1.default.config();
const app = (0, express_1.default)();
(0, security_1.applySecurity)(app);
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.get('/health', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Server is running',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
    });
});
const apiVersion = process.env.API_VERSION || 'v1';
app.use(`/api/${apiVersion}/auth`, auth_routes_1.default);
app.use(`/api/${apiVersion}/users`, user_routes_1.default);
app.get('/', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Welcome to the Node.js TypeScript API Boilerplate',
        version: apiVersion,
        documentation: `/api/${apiVersion}/docs`,
        endpoints: {
            auth: `/api/${apiVersion}/auth`,
            users: `/api/${apiVersion}/users`,
            health: '/health',
        },
    });
});
app.use(errorHandler_1.notFound);
app.use(errorHandler_1.errorHandler);
process.on('SIGTERM', () => {
    logger_1.logger.info('SIGTERM received. Shutting down gracefully...');
    process.exit(0);
});
process.on('SIGINT', () => {
    logger_1.logger.info('SIGINT received. Shutting down gracefully...');
    process.exit(0);
});
exports.default = app;
//# sourceMappingURL=app.js.map