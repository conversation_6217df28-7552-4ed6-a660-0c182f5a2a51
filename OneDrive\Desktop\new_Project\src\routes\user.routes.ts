import { Router } from 'express';
import { body, param } from 'express-validator';
import {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  getProfile,
} from '@controllers/user.controller';
import { protect, authorize, ownerOrAdmin } from '@middleware/auth';
import { validate } from '@utils/validation';

const router = Router();

// Get current user profile
router.get('/profile', protect, getProfile);

// Get all users (admin only)
router.get('/', protect, authorize('admin'), getUsers);

// Get single user
router.get(
  '/:id',
  protect,
  validate([param('id').isMongoId().withMessage('Please provide a valid ID')]),
  ownerOrAdmin,
  getUser
);

// Create user (admin only)
router.post(
  '/',
  protect,
  authorize('admin'),
  validate([
    body('firstName')
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2 and 50 characters')
      .trim()
      .escape(),
    body('lastName')
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2 and 50 characters')
      .trim()
      .escape(),
    body('email')
      .isEmail()
      .withMessage('Please provide a valid email address')
      .normalizeEmail(),
    body('password')
      .isLength({ min: 6 })
      .withMessage('Password must be at least 6 characters long'),
    body('role')
      .optional()
      .isIn(['user', 'admin'])
      .withMessage('Role must be either user or admin'),
  ]),
  createUser
);

// Update user
router.put(
  '/:id',
  protect,
  validate([
    param('id').isMongoId().withMessage('Please provide a valid ID'),
    body('firstName')
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2 and 50 characters')
      .trim()
      .escape(),
    body('lastName')
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2 and 50 characters')
      .trim()
      .escape(),
    body('email')
      .optional()
      .isEmail()
      .withMessage('Please provide a valid email address')
      .normalizeEmail(),
    body('role')
      .optional()
      .isIn(['user', 'admin'])
      .withMessage('Role must be either user or admin'),
    body('isActive')
      .optional()
      .isBoolean()
      .withMessage('isActive must be a boolean'),
  ]),
  ownerOrAdmin,
  updateUser
);

// Delete user (admin only)
router.delete(
  '/:id',
  protect,
  authorize('admin'),
  validate([param('id').isMongoId().withMessage('Please provide a valid ID')]),
  deleteUser
);

export default router;
