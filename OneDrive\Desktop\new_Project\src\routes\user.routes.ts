import { Router } from 'express';
import {
  getUsers,
  getUser,
  getUserProfile,
  createUser,
  updateUser,
  deleteUser,
  searchUsers,
  getUsersByRole,
  restoreUser,
  getUserStats,
} from '@controllers/user.controller';
import { protect, authorize, ownerOrAdmin } from '@middleware/auth';
import { validate } from '@utils/validation';
import {
  validateGetUsers,
  validateGetUserById,
  validateCreateUser,
  validateUpdateUser,
  validateDeleteUser,
  validateSearchUsers,
  validateGetUsersByRole,
  validateRestoreUser,
  validateGetUserProfile,
} from '@validators/user.validator';

const router = Router();

// Get current user profile
router.get('/profile', protect, getUserProfile);

// Get all users (admin only)
router.get('/', protect, authorize('admin'), validate(validateGetUsers), getUsers);

// Search users (admin only)
router.get('/search', protect, authorize('admin'), validate(validateSearchUsers), searchUsers);

// Get user statistics (admin only)
router.get('/stats', protect, authorize('admin'), getUserStats);

// Get users by role (admin only)
router.get('/role/:role', protect, authorize('admin'), validate(validateGetUsersByRole), getUsersByRole);

// Get user by ID
router.get('/:id', protect, validate(validateGetUserById), getUser);

// Create user (admin only)
router.post('/', protect, authorize('admin'), validate(validateCreateUser), createUser);

// Update user
router.put('/:id', protect, validate(validateUpdateUser), ownerOrAdmin, updateUser);

// Restore user (admin only)
router.put('/:id/restore', protect, authorize('admin'), validate(validateRestoreUser), restoreUser);

// Delete user (admin only)
router.delete('/:id', protect, authorize('admin'), validate(validateDeleteUser), deleteUser);

export default router;
