{"env": {"es2021": true, "node": true, "jest": true}, "extends": ["eslint:recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-inferrable-types": "off", "@typescript-eslint/no-non-null-assertion": "warn", "prefer-const": "error", "no-var": "error", "no-console": "off", "eqeqeq": "error", "curly": "error"}, "ignorePatterns": ["dist/", "node_modules/", "coverage/", "*.js"]}