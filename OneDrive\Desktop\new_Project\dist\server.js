"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = __importDefault(require("./app"));
const database_1 = require("@config/database");
const logger_1 = require("@utils/logger");
const PORT = process.env.PORT || 5000;
const startServer = async () => {
    try {
        await (0, database_1.connectDB)();
        const server = app_1.default.listen(PORT, () => {
            logger_1.logger.info(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
        });
        process.on('unhandledRejection', (err) => {
            logger_1.logger.error('Unhandled Promise Rejection:', err);
            server.close(() => {
                process.exit(1);
            });
        });
        process.on('uncaughtException', (err) => {
            logger_1.logger.error('Uncaught Exception:', err);
            process.exit(1);
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to start server:', error);
        process.exit(1);
    }
};
startServer();
//# sourceMappingURL=server.js.map