{"name": "@types/serve-static", "version": "1.15.8", "description": "TypeScript definitions for serve-static", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "urossmolnik", "url": "https://github.com/urossmolnik"}, {"name": "<PERSON><PERSON>", "githubUsername": "LinusU", "url": "https://github.com/LinusU"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/devanshj"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-static"}, "scripts": {}, "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "9ececac158c4f4a101c67a8445ba5ed874de4a77e2e20fae569f6553b86ad082", "typeScriptVersion": "5.1"}