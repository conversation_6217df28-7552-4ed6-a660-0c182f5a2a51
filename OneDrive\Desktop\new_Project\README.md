# Node.js TypeScript API Boilerplate

A comprehensive, production-ready Node.js TypeScript API boilerplate with MongoDB, authentication, comprehensive tooling, and best practices.

## 🚀 Features

- **TypeScript**: Full TypeScript support with strict type checking and path mapping
- **Express.js**: Fast, unopinionated web framework with comprehensive middleware
- **MongoDB**: NoSQL database with Mongoose ODM and proper schema design
- **Authentication**: JWT-based authentication with role-based access control
- **Security**: Helmet, CORS, rate limiting, input validation, and sanitization
- **Code Quality**: ESLint, Prettier, and pre-commit hooks with <PERSON>sky
- **Testing**: Jest with TypeScript support and example tests
- **Development**: Hot reload, comprehensive logging, and error handling
- **Production Ready**: Environment configuration, graceful shutdown, and deployment scripts

## 📋 Prerequisites

- Node.js (>= 16.0.0)
- npm (>= 8.0.0)
- MongoDB (local installation or MongoDB Atlas)
- Git

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd new_project
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   Update the `.env` file with your configuration:
   - `MONGO_URI`: Your MongoDB connection string
   - `JWT_SECRET`: A secure secret for JWT tokens (use a strong, random string)
   - Other environment variables as needed

4. **Start the development server**
   ```bash
   npm run dev
   ```

## 📁 Project Structure

```
src/
├── config/          # Configuration files
│   └── database.ts  # Database connection setup
├── controllers/     # Route controllers with business logic
│   ├── auth.controller.ts
│   └── user.controller.ts
├── middleware/      # Custom middleware
│   ├── auth.ts      # Authentication middleware
│   ├── errorHandler.ts
│   └── security.ts  # Security middleware bundle
├── models/          # Mongoose models and schemas
│   └── User.model.ts
├── routes/          # API route definitions
│   ├── auth.routes.ts
│   └── user.routes.ts
├── services/        # Business logic services
├── types/           # TypeScript type definitions
│   ├── environment.d.ts
│   └── user.types.ts
├── utils/           # Utility functions and helpers
│   ├── AppError.ts  # Custom error classes
│   ├── logger.ts    # Logging utility
│   └── validation.ts
├── tests/           # Test files
│   ├── setup.ts
│   └── app.test.ts
├── app.ts           # Express app configuration
└── server.ts        # Server entry point
```

## 🔧 Available Scripts

### **Development Commands**
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build the project for production
- `npm start` - Start production server (after build)
- `npm run start:dev` - Build and start production server
- `npm run clean` - Clean build directory

### **Code Quality Commands**
- `npm run lint` - Run ESLint to check for code issues
- `npm run lint:fix` - Auto-fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check if code is properly formatted

### **Testing Commands**
- `npm test` - Run all tests
- `npm run test:watch` - Run tests in watch mode (re-runs on file changes)
- `npm run test:coverage` - Run tests with coverage report

## 🌐 API Endpoints

### Base URL
```
http://localhost:5000/api/v1
```

### Authentication
- `POST /auth/register` - Register a new user
- `POST /auth/login` - Login user
- `GET /auth/me` - Get current user profile
- `PUT /auth/updatedetails` - Update user details
- `PUT /auth/updatepassword` - Update password
- `GET /auth/logout` - Logout user

### Users
- `GET /users` - Get all users (admin only)
- `GET /users/profile` - Get current user profile
- `GET /users/:id` - Get user by ID
- `POST /users` - Create user (admin only)
- `PUT /users/:id` - Update user
- `DELETE /users/:id` - Delete user (admin only)

### System
- `GET /health` - Health check endpoint

## 🔒 Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Environment mode | `development` | No |
| `PORT` | Server port | `5000` | No |
| `MONGO_URI` | MongoDB connection string | - | Yes |
| `JWT_SECRET` | JWT secret key | - | Yes |
| `JWT_EXPIRE` | JWT expiration time | `7d` | No |
| `CORS_ORIGIN` | CORS allowed origin | `http://localhost:3000` | No |
| `LOG_LEVEL` | Logging level | `info` | No |

## 🧪 Testing

Run the test suite:
```bash
npm test
```

Run tests with coverage:
```bash
npm run test:coverage
```

Run tests in watch mode:
```bash
npm run test:watch
```

## 🔧 Code Quality

This project includes comprehensive code quality tools:

- **ESLint**: Linting with TypeScript support
- **Prettier**: Code formatting
- **Husky**: Git hooks for pre-commit checks
- **lint-staged**: Run linters on staged files

Pre-commit hooks automatically:
- Run ESLint and fix issues
- Format code with Prettier
- Ensure code quality before commits

## 🚀 Usage Scenarios & Commands

### **1. Starting Development**
```bash
# Make sure MongoDB is running locally, then:
npm run dev
```
Your server will start at `http://localhost:5000`

### **2. Before Committing Code**
```bash
# Check code quality
npm run lint
npm run format:check
npm test

# If all pass, commit (pre-commit hooks will run automatically)
git add .
git commit -m "Your commit message"
```

### **3. Preparing for Production**
```bash
# Build the project
npm run build

# Test the production build
npm run start:dev

# Or start production server directly
npm start
```

### **4. Adding New Features**
```bash
# Start development server
npm run dev

# In another terminal, run tests in watch mode
npm run test:watch

# Write your code, tests will auto-run
# Lint and format will run on commit
```

### **5. API Testing**
Your API endpoints are available at:
- **Base URL**: `http://localhost:5000/api/v1`
- **Health Check**: `GET http://localhost:5000/health`
- **Auth**: `POST http://localhost:5000/api/v1/auth/register`
- **Auth**: `POST http://localhost:5000/api/v1/auth/login`
- **Users**: `GET http://localhost:5000/api/v1/users` (requires auth)

### **6. Environment Setup**
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings:
# - MONGO_URI (your MongoDB connection string)
# - JWT_SECRET (a secure random string)
# - Other configuration as needed
```

## 🔄 Quick Start Workflow

### **First Time Setup**
```bash
npm install
cp .env.example .env
# Edit .env with your MongoDB URI and JWT secret
```

### **Daily Development**
```bash
npm run dev
# Start coding!
```

### **Before Committing**
```bash
npm test
git add .
git commit -m "Your message"
# Pre-commit hooks run automatically
```

### **Deployment**
```bash
npm run build
npm start
```

## 🛠️ Additional Useful Commands

```bash
# Install new dependencies
npm install package-name

# Install dev dependencies
npm install --save-dev package-name

# Update dependencies
npm update

# Check for outdated packages
npm outdated

# Run specific test file
npm test -- --testPathPattern=auth.test.ts

# Run tests with specific pattern
npm test -- --testNamePattern="should register"

# Git & Pre-commit Commands
git init                    # Initialize git repository
git add .                   # Add files to staging
git commit -m "message"     # Commit (pre-commit hooks run automatically)
```

## 🛡️ Security Features

- **Helmet**: Security headers
- **CORS**: Cross-origin resource sharing
- **Rate Limiting**: Prevent abuse
- **Input Validation**: Request validation with express-validator
- **Input Sanitization**: Prevent injection attacks
- **JWT Authentication**: Secure token-based auth
- **Password Hashing**: bcrypt for password security
- **Error Handling**: Secure error responses

## 📝 Development Guidelines

- Follow TypeScript best practices
- Use ESLint and Prettier for code quality
- Write tests for new features
- Follow RESTful API conventions
- Use meaningful commit messages
- Document your code
- Handle errors properly
- Use environment variables for configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the ISC License.

## 🆘 Support

If you have any questions or need help, please open an issue in the repository.

## 🎯 Next Steps

Your boilerplate is production-ready! You can now:

1. **Start building your API** by adding new models, controllers, and routes
2. **Add more middleware** as needed for your specific use case
3. **Extend the User model** with additional fields
4. **Add more test cases** for your new features
5. **Set up CI/CD** with your preferred platform
6. **Deploy to production** (Heroku, AWS, DigitalOcean, etc.)

The setup includes everything you need for a professional Node.js TypeScript API with proper code quality, testing, and security measures!

## 🔄 Changelog

### v1.0.0
- ✅ Complete boilerplate setup with Node.js + TypeScript
- ✅ MongoDB integration with Mongoose ODM
- ✅ JWT authentication system with role-based access
- ✅ Comprehensive security middleware (CORS, Helmet, Rate limiting)
- ✅ Code quality tools (ESLint, Prettier, Husky pre-commit hooks)
- ✅ Testing framework with Jest and TypeScript support
- ✅ Error handling and logging utilities
- ✅ Production-ready configuration and deployment scripts
- ✅ Comprehensive documentation and command reference
